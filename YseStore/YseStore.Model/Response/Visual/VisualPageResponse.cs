using Entitys;

namespace YseStore.Model.Response.Visual
{
    /// <summary>
    /// 可视化页面响应模型
    /// </summary>
    public class VisualPageResponse
    {
        /// <summary>
        /// 插件列表
        /// </summary>
        // public List<visual_plugins> Plugins { get; set; }

        /// <summary>
        /// 按类型分组的插件
        /// </summary>
        public Dictionary<string, List<VisualPluginDto>> PluginsByType { get; set; }

        public VisualPageResponse()
        {
            // Plugins = new List<visual_plugins>();
            PluginsByType = new Dictionary<string, List<VisualPluginDto>>();
        }
    }

    /// <summary>
    /// 可视化页面请求参数
    /// </summary>
    public class VisualPageRequest
    {
        /// <summary>
        /// 页面类型（如：index、products等）
        /// </summary>
        public string Pages { get; set; } = "index";
    }

    /// <summary>
    /// 可视化插件数据传输对象
    /// </summary>
    public class VisualPluginDto
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public int PId { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 风格
        /// </summary>
        public string Mode { get; set; } = string.Empty;

        /// <summary>
        /// 设置（JSON字符串）
        /// </summary>
        public string Settings { get; set; } = string.Empty;

        /// <summary>
        /// 内容模块（JSON字符串）
        /// </summary>
        public string Blocks { get; set; } = string.Empty;

        /// <summary>
        /// 插件配置（JSON字符串）
        /// </summary>
        public string Config { get; set; } = string.Empty;

        /// <summary>
        /// 解析后的设置对象（用于前端）
        /// </summary>
        public object? ParsedSettings { get; set; }

        /// <summary>
        /// 解析后的内容模块对象（用于前端）
        /// </summary>
        public object? ParsedBlocks { get; set; }

        /// <summary>
        /// 解析后的配置对象（用于前端）
        /// </summary>
        public object? ParsedConfig { get; set; }
    }
}
