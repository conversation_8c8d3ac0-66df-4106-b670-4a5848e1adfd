using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text.Json;
using YseStore.Common.Cache;
using YseStore.IService.Visual;
using YseStore.Model.Response.Visual;

namespace YseStore.Service.Visual
{
    /// <summary>
    /// 可视化页面构建服务
    /// </summary>
    public class VisualPageBuilderService : IVisualPageBuilderService
    {
        private readonly IVisualDraftsService _visualDraftsService;
        private readonly IVisualTemplateServicee _visualTemplateService;
        private readonly IVisualPagesService _visualPagesService;
        private readonly IVisualPluginsService _visualPluginsService;
        private readonly ICaching _caching;
        private readonly ILogger<VisualPageBuilderService> _logger;

        public VisualPageBuilderService(
            IVisualDraftsService visualDraftsService,
            IVisualTemplateServicee visualTemplateService,
            IVisualPagesService visualPagesService,
            IVisualPluginsService visualPluginsService,
            ICaching caching,
            ILogger<VisualPageBuilderService> logger)
        {
            _visualDraftsService = visualDraftsService;
            _visualTemplateService = visualTemplateService;
            _visualPagesService = visualPagesService;
            _visualPluginsService = visualPluginsService;
            _caching = caching;
            _logger = logger;
        }

        /// <summary>
        /// 根据页面类型构建可视化页面数据
        /// </summary>
        /// <param name="pages">页面类型（如：index、products等）</param>
        /// <returns></returns>
        public async Task<VisualPageResponse> BuildVisualPageAsync(string pages)
        {
            try
            {
                var response = new VisualPageResponse();

                // 1. 获取最新的可见草稿
                var draft = await _visualDraftsService.GetLatestVisibleDraftAsync();
                if (draft == null)
                {
                    _logger.LogWarning("未找到可见的草稿数据");
                    return response;
                }

                // 2. 根据DraftsId和页面类型获取模板
                var template = await _visualTemplateService.GetTemplateByDraftsIdAndPagesAsync(draft.DraftsId, pages);
                if (template == null)
                {
                    _logger.LogWarning("未找到对应的模板数据，DraftsId: {DraftsId}, Pages: {Pages}", draft.DraftsId, pages);
                    return response;
                }

                // 3. 根据TemplateId获取页面配置
                var page = await _visualPagesService.GetPagesByTemplateIdAsync(template.TemplateId);
                if (page == null)
                {
                    _logger.LogWarning("未找到对应的页面配置，TemplateId: {TemplateId}", template.TemplateId);
                    return response;
                }

                // 4. 解析插件ID数组并获取插件列表
                if (!string.IsNullOrEmpty(page.Plugins))
                {
                    try
                    {
                        // 使用 System.Text.Json 进行统一的序列化处理
                        var pluginIds = System.Text.Json.JsonSerializer.Deserialize<List<int>>(page.Plugins, JOptions.Default);
                        if (pluginIds != null && pluginIds.Any())
                        {
                            var plugins = await _visualPluginsService.GetPluginsByIdsAsync(pluginIds);

                            // 按Type分组插件，并确保数据结构的正确性
                            response.PluginsByType = plugins
                                .Where(p => !string.IsNullOrEmpty(p.Type))
                                .GroupBy(p => p.Type)
                                .ToDictionary(g => g.Key, g => g.Select(plugin => CreateVisualPluginDto(plugin)).ToList());

                            _logger.LogInformation("成功构建可视化页面数据，Pages: {Pages}, 插件类型数量: {TypeCount}",
                                pages, response.PluginsByType.Count);
                        }
                    }
                    catch (System.Text.Json.JsonException ex)
                    {
                        _logger.LogError(ex, "解析插件ID数组失败，Plugins: {Plugins}", page.Plugins);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理插件数据失败，Plugins: {Plugins}", page.Plugins);
                    }
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建可视化页面数据失败，Pages: {Pages}", pages);
                return new VisualPageResponse();
            }
        }

        /// <summary>
        /// 获取页面插件配置数据
        /// </summary>
        /// <param name="pages">页面类型</param>
        /// <returns></returns>
        public async Task<Dictionary<string, object>> GetPagePluginConfigAsync(string pages)
        {
            try
            {
                var cacheKey = $"visual_page_config_{pages}";
                
                // 尝试从缓存获取
                var cachedConfig = await _caching.GetAsync<Dictionary<string, object>>(cacheKey);
                if (cachedConfig != null)
                {
                    return cachedConfig;
                }

                // var visualPage = await BuildVisualPageAsync(pages);
                var config = new Dictionary<string, object>();

                // if (visualPage.Plugins.Any())
                // {
                //     config["plugins"] = visualPage.Plugins;
                //     config["pluginsByType"] = visualPage.PluginsByType;
                // }

                // 缓存配置数据（缓存10分钟）
                await _caching.SetAsync(cacheKey, config, TimeSpan.FromMinutes(10));

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取页面插件配置数据失败，Pages: {Pages}", pages);
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// 创建可视化插件DTO对象，并解析JSON字符串
        /// </summary>
        /// <param name="plugin">原始插件实体</param>
        /// <returns>插件DTO对象</returns>
        private VisualPluginDto CreateVisualPluginDto(Entitys.visual_plugins plugin)
        {
            var dto = new VisualPluginDto
            {
                PId = plugin.PId,
                Type = plugin.Type ?? string.Empty,
                Mode = plugin.Mode ?? string.Empty,
                Settings = plugin.Settings ?? string.Empty,
                Blocks = plugin.Blocks ?? string.Empty,
                Config = plugin.Config ?? string.Empty
            };

            // 尝试解析 Settings JSON 字符串
            if (!string.IsNullOrEmpty(plugin.Settings))
            {
                try
                {
                    dto.ParsedSettings = System.Text.Json.JsonSerializer.Deserialize<object>(plugin.Settings, JOptions.Default);
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "解析插件Settings失败，PId: {PId}, Settings: {Settings}", plugin.PId, plugin.Settings);
                    dto.ParsedSettings = null;
                }
            }

            // 尝试解析 Blocks JSON 字符串
            if (!string.IsNullOrEmpty(plugin.Blocks))
            {
                try
                {
                    dto.ParsedBlocks = System.Text.Json.JsonSerializer.Deserialize<object>(plugin.Blocks, JOptions.Default);
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "解析插件Blocks失败，PId: {PId}, Blocks: {Blocks}", plugin.PId, plugin.Blocks);
                    dto.ParsedBlocks = null;
                }
            }

            // 尝试解析 Config JSON 字符串
            if (!string.IsNullOrEmpty(plugin.Config))
            {
                try
                {
                    dto.ParsedConfig = System.Text.Json.JsonSerializer.Deserialize<object>(plugin.Config, JOptions.Default);
                }
                catch (System.Text.Json.JsonException ex)
                {
                    _logger.LogWarning(ex, "解析插件Config失败，PId: {PId}, Config: {Config}", plugin.PId, plugin.Config);
                    dto.ParsedConfig = null;
                }
            }

            return dto;
        }
    }
}
