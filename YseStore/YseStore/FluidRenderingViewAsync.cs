using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using Fluid;
using Fluid.Values;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Fluid.Filters;
using Entitys;
using YseStore.Common;
using SqlSugar.Extensions;
using YseStore.Common.Cache;
using Wangkanai.Detection.Services;
using YseStore.IService.SiteSystem;
using YseStore.Filter;

namespace YseStore;

internal static class FluidRenderingViewAsync
{
    // 注册translate过滤器
    public static void RegisterFilters(FluidParser parser, TemplateOptions options)
    {

        // 注册translate过滤器，使其与T函数功能相同
        options.Filters.AddFilter("translate", (input, arguments, context) =>
        {
            // 直接使用IStringLocalizer本地化字符串
            if (context.AmbientValues.TryGetValue("StringLocalizer", out var localizerObj) &&
                localizerObj is IStringLocalizer localizer)
            {
                var text = input.ToStringValue();
                return new ValueTask<FluidValue>(new StringValue(localizer[text]));
            }

            return new ValueTask<FluidValue>(input);
        });

        options.Filters.AddFilter("hasflag", (i, args, ctx) =>
        {
            var s = i.ToNumberValue().ObjToInt();

            var flag = 0;
            if (args.Count > 0)
            {
                flag = args.At(0).ToNumberValue().ObjToInt();
            }
            return NumberValue.Create(s & flag);
        });


        options.Filters.AddFilter("jsonparse", JsonParseFilter.JsonParse);

        // 添加不转义的JSON序列化过滤器
        options.Filters.AddFilter("json_raw", (input, arguments, context) =>
        {
            try
            {
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = null, // 保持原始属性名
                    WriteIndented = false,
                    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                    ReferenceHandler = ReferenceHandler.IgnoreCycles
                };

                var objectValue = input.ToObjectValue();
                var jsonString = System.Text.Json.JsonSerializer.Serialize(objectValue, jsonOptions);

                // 返回原始字符串值，不进行 HTML 编码
                return new ValueTask<FluidValue>(new StringValue(jsonString) { Encode = false });
            }
            catch (Exception ex)
            {
                // 记录错误但返回空对象
                System.Diagnostics.Debug.WriteLine($"json_raw 过滤器序列化失败: {ex.Message}");
                return new ValueTask<FluidValue>(new StringValue("{}") { Encode = false });
            }
        });
    }

    internal static async ValueTask AddItemsToFluidContext(string path, ViewContext viewContext, TemplateContext context)
    {
        var mxContext = viewContext.HttpContext.RequestServices.GetService<IHttpContextAccessor>();
        var cachingService = viewContext.HttpContext.RequestServices.GetService<ICaching>();
        var detectionService = viewContext.HttpContext.RequestServices.GetService<IDetectionService>();
        var req = viewContext.HttpContext.Request;

        var thridService = viewContext.HttpContext.RequestServices.GetService<IThirdService>();
        //获取缓存中的自定义代码列表
        var thridList = await thridService.GetAllThriedCache();
        thridList = thridList.Where(it => it.IsUsed == true && it.Trigger == "all").ToList(); //过滤掉未启用的代码
        var PCheadThridList = thridList.Where(it => it.CodeType != 2 && it.IsMeta == true).Select(it => it.Code).ToList(); //桌面端header
        var PCbodyThridList = thridList.Where(it => it.CodeType != 2 && it.IsBody == true).Select(it => it.Code).ToList(); //桌面端body
        var MBheadThridList = thridList.Where(it => it.CodeType != 1 && it.IsMeta == true).Select(it => it.Code).ToList(); //手机端header
        var MBbodyThridList = thridList.Where(it => it.CodeType != 1 && it.IsBody == true).Select(it => it.Code).ToList(); //手机端body


        var isLogined = viewContext.HttpContext.User != null && viewContext.HttpContext.User.Identity != null && viewContext.HttpContext.User.Identity.IsAuthenticated;
        var isUseDesktop = detectionService.Device.Type == Wangkanai.Detection.Models.Device.Desktop;
        context.SetValue("IsUseDesktop", isUseDesktop);

        context.SetValue("IsLogined", isLogined);
        if (isUseDesktop)
        {

            var headCode = string.Join(" ", PCheadThridList);
            context.SetValue("HeadCode", headCode);
            var bodyCode = string.Join(" ", PCbodyThridList);
            context.SetValue("BodyCode", bodyCode);
        }
        else
        {
            // 移动端
            var headCode = string.Join(" ", MBheadThridList);
            context.SetValue("HeadCode", headCode);
            var bodyCode = string.Join(" ", MBbodyThridList);
            context.SetValue("BodyCode", bodyCode);
        }


        //context.SetValue("ViewData", viewContext.ViewData);
        //context.SetValue("ModelState", viewContext.ModelState);
        //context.SetValue("Model", viewContext.ViewData.Model);
        context.SetValue("WebSiteDomain", AppSettings.GetValue("WebSiteDomain"));
#if DEBUG
        var themeName = Environment.GetEnvironmentVariable("Theme");
        var bodyCss = Environment.GetEnvironmentVariable("BodyCss");
        var bodyId = Environment.GetEnvironmentVariable("BodyId");

#endif
#if !DEBUG
        var themeName = AppSettings.GetValue("Theme");
        var bodyCss = AppSettings.GetValue("BodyCss");
        var bodyId = AppSettings.GetValue("BodyId");
    
#endif
        //渠道官网
        //var theme = new { Name= "t300", BodyCss= "home-2"};
        //retevis 
        //var theme = new { Name = "t100", BodyCss = "template-index index-retevis-1 modal-popup-style" };

        //retekess 助力听器
        var theme = new { Name = themeName, BodyCss = bodyCss, BodyId = bodyId };
        var ResPath = $"{req.Scheme}://{req.Host}{req.Path}";
        var ResHost = $"{req.Scheme}://{req.Host}";
        var mxLang = viewContext.HttpContext.RequestServices.GetRequiredService<IStringLocalizer>();
        var isAjax = viewContext.HttpContext.Request.Headers["HX-Request"] == "true";
        var T = new FunctionValue((args, context) =>
        {
            var firstArg = args.At(0).ToStringValue();
            return new ValueTask<FluidValue>(new StringValue(mxLang[firstArg]));
        });
        context.SetValue("theme", theme.Name);
        context.SetValue("bodycss", theme.BodyCss);
        context.SetValue("bodyid", theme.BodyId);

        context.SetValue("static_path", $"/static/themes/{theme.Name}");

        context.SetValue("MxContext", mxContext);
        context.SetValue("IsAjax", isAjax);

        context.SetValue("T", T);
        context.SetValue("ResPath", ResPath);

        // 添加StringLocalizer到AmbientValues中供过滤器使用
        context.AmbientValues.Add("StringLocalizer", mxLang);

        await Task.CompletedTask;
    }
}

